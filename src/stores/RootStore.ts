import { ReportByPlantStore } from 'features/ButtonMailingReport/model'
import { AuthStore } from 'stores/AuthStore/AuthStore'
import { ListOfStationsStore } from 'stores/CalcModelStore/ListOfStationsStore'
import { CalcModelWerStore } from 'stores/CalcModelWerStore'
import { CalculationsPageStore } from 'stores/CalculationsPageStore'
import { VaultStore } from 'stores/CalculationsPageStore/VaultStore/VaultStore.ts'
import { GodModeStore } from 'stores/GodModeStore'
import { NotificationStore } from 'stores/NotificationStore/NotificationStore'
import { NsiStore } from 'stores/NsiStore/NsiStore'
import { SettingsStore } from 'stores/SettingsStore/SettingsStore'

import { AccessControlStore } from './AccessControlStore/AccessControlStore'
import { CalcModelStore } from './CalcModelStore'
import { WerListOfCascadesStore } from './CalcModelWerStore/WerListOfCascadesStore/WerListOfCascadesStore.ts'
import { WerListOfStationsStore } from './CalcModelWerStore/WerListOfStationsStore'
import { WerCharacteristicsStore } from './CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerCharacteristicsStore.ts'
import { WerReservoirVolumeStore } from './CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerReservoirVolumeStore/WerReservoirVolumeStore.ts'
import { WerSpecificConsumptionStore } from './CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerSpecificConsumptionStore'
import { WerTailraceStore } from './CalcModelWerStore/WerListOfStationsStore/WerCharacteristicsStore/WerTailraceStore'
import { WerParametersStore } from './CalcModelWerStore/WerListOfStationsStore/WerParametersStore'
import { WerRestrictionsStore } from './CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore'
import { WerRunningTimesStore } from './CalcModelWerStore/WerRunningTimesStore/WerRunningTimesStore.ts'
import { AvrchmStore } from './CalculationsPageStore/AvrchmStore.ts'
import { GAESCalculationsStore } from './GAESCalculationsStore'
import { GAESArchiveStore } from './GAESCalculationsStore/GAESArchiveStore'
import { GAESCalculationTabStore } from './GAESCalculationsStore/GAESCalculationTabStore'
import { JournalsStore } from './JournalsStore/JournalsStore'
import { IndicatorStore } from './NsiStore/IndicatorStore.ts'
import { ListOfCascadesStore } from './NsiStore/ListOfCascadesStore'
import { ListOfRestrictionsStore } from './NsiStore/ListOfRestrictionsStore'
import { ListOfTerritoriesStore } from './NsiStore/ListOfTerritoriesStore'
import { ReferenceDataStore } from './NsiStore/ReferenceDataStore.ts'
import { ReportsStore } from './ReportsStore/ReportsStore'
import { ApiStore } from './SettingsStore/ApiStore'
import { DataStorageDepthStore } from './SettingsStore/DataStorageDepthStore'
import { ExternalSystemStore } from './SettingsStore/ExternalSystemStore'

export class RootStore {
  authStore: AuthStore
  notificationStore: NotificationStore
  accessControlStore: AccessControlStore
  nsiStore: NsiStore
  listOfRestrictionsStore: ListOfRestrictionsStore
  listOfTerritoriesStore: ListOfTerritoriesStore
  calcModelStore: CalcModelStore
  calcModelWerStore: CalcModelWerStore
  calculationsPageStore: CalculationsPageStore
  settingsStore: SettingsStore
  externalSystemStore: ExternalSystemStore
  settingsApiStore: ApiStore
  settingsDataStorageDepthStore: DataStorageDepthStore
  reportsStore: ReportsStore
  journalsStore: JournalsStore
  listOfCascadesStore: ListOfCascadesStore
  gaesCalculationsStore: GAESCalculationsStore
  referenceDataStore: ReferenceDataStore
  indicatorStore: IndicatorStore
  reportByPlantStore: ReportByPlantStore
  godModeStore: GodModeStore

  constructor() {
    this.authStore = new AuthStore()
    this.notificationStore = new NotificationStore()
    this.accessControlStore = new AccessControlStore(this)
    this.nsiStore = new NsiStore(this)
    this.listOfRestrictionsStore = new ListOfRestrictionsStore(this)
    this.listOfTerritoriesStore = new ListOfTerritoriesStore(this)
    this.calcModelStore = new CalcModelStore(this, new ListOfStationsStore(this))
    this.calcModelWerStore = new CalcModelWerStore(
      this,
      new WerListOfStationsStore(
        this,
        new WerParametersStore(this),
        new WerRestrictionsStore(this),
        new WerCharacteristicsStore(
          this,
          new WerReservoirVolumeStore(this),
          new WerTailraceStore(this),
          new WerSpecificConsumptionStore(this),
        ),
      ),
      new WerListOfCascadesStore(this),
      new WerRunningTimesStore(this),
    )
    this.calculationsPageStore = new CalculationsPageStore(this, new AvrchmStore(this), new VaultStore(this))
    this.settingsStore = new SettingsStore(this)
    this.externalSystemStore = new ExternalSystemStore(this)
    this.settingsApiStore = new ApiStore(this)
    this.settingsDataStorageDepthStore = new DataStorageDepthStore(this)
    this.reportsStore = new ReportsStore(this)
    this.journalsStore = new JournalsStore(this)
    this.listOfCascadesStore = new ListOfCascadesStore(this)
    this.gaesCalculationsStore = new GAESCalculationsStore(
      this,
      new GAESArchiveStore(this),
      new GAESCalculationTabStore(this),
    )
    this.referenceDataStore = new ReferenceDataStore(this)
    this.indicatorStore = new IndicatorStore(this)
    this.reportByPlantStore = new ReportByPlantStore(this)
    this.godModeStore = new GodModeStore()
  }
}

declare global {
  interface Window {
    _____APP_STATE_____: RootStore
  }
}

const stores = new RootStore()
window._____APP_STATE_____ = stores
export { stores }
